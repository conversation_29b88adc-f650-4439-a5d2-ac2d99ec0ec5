# 多進程系統部署指南

## 🚀 快速開始

### 一鍵部署

```bash
# 1. 克隆代碼
git clone <repository-url> outlook-summary
cd outlook-summary

# 2. 確保前端重構已完成
# 檢查 frontend/ 目錄是否存在且包含模組化結構
ls -la frontend/

# 3. 給予執行權限
chmod +x deployment/manage.sh

# 4. 選擇部署方式
deployment/manage.sh deploy-compose    # Docker Compose (推薦)
deployment/manage.sh deploy-docker     # Docker
deployment/manage.sh deploy-native     # 原生部署
```

### 驗證部署

```bash
# 檢查服務狀態
deployment/manage.sh status

# 健康檢查
deployment/manage.sh health

# 查看日誌
deployment/manage.sh logs
```

## 📋 部署方式對比

| 方式 | 優點 | 缺點 | 適用場景 |
|------|------|------|----------|
| **Docker Compose** | 一鍵部署、完整監控 | 需要Docker | 生產環境 (推薦) |
| **Docker** | 輕量、隔離性好 | 無監控組件 | 開發測試 |
| **原生部署** | 性能最佳、直接控制 | 配置複雜 | 高性能需求 |

## 🏗️ 系統架構

### 新架構 (前端重構後)

```
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   Nginx         │  │   Flask Frontend│  │   Redis         │
│   (反向代理)     │  │   (模組化架構)   │  │   (進程通訊)     │
│   Port: 80      │  │   Port: 8000    │  │   Port: 6379    │
└─────────────────┘  └─────────────────┘  └─────────────────┘
         │                      │                      │
         └──────────────────────┼──────────────────────┘
                                │
         ┌─────────────────┐    │    ┌─────────────────┐
         │   Prometheus    │    │    │   Grafana       │
         │   (指標收集)     │────┘    │   (監控儀表板)   │
         │   Port: 9090    │         │   Port: 3000    │
         └─────────────────┘         └─────────────────┘
```

### 前端模組架構

```
frontend/
├── email/           # 郵件管理模組
├── analytics/       # 分析統計模組  
├── file_management/ # 檔案管理模組
├── eqc/            # EQC品質控制模組
├── tasks/          # 任務管理模組
├── monitoring/     # 系統監控模組
├── shared/         # 共享資源模組
└── app.py          # Flask 主應用程式
```

## ⚙️ 環境要求

### 最低要求

- **CPU**: 4核心
- **記憶體**: 8GB
- **磁碟**: 50GB SSD
- **作業系統**: Linux (Ubuntu 20.04+ / CentOS 8+)

### 推薦配置

- **CPU**: 8核心16線程
- **記憶體**: 16GB
- **磁碟**: 100GB NVMe SSD
- **網路**: 1Gbps

### 高性能配置

- **CPU**: 16核心32線程+
- **記憶體**: 32GB+
- **磁碟**: 500GB NVMe SSD RAID
- **網路**: 10Gbps

## 🐳 Docker Compose 部署 (推薦)

### 特點
- ✅ 完整監控棧 (Prometheus + Grafana)
- ✅ 自動服務發現
- ✅ 負載均衡
- ✅ 健康檢查
- ✅ 日誌聚合

### 部署步驟

```bash
# 1. 部署
deployment/manage.sh deploy-compose

# 2. 驗證
deployment/manage.sh status
deployment/manage.sh health

# 3. 訪問服務
# 應用: http://localhost:8000
# 監控: http://localhost:9090  
# 儀表板: http://localhost:3000 (admin/admin123)
```

### 管理命令

```bash
# 服務管理
deployment/manage.sh start          # 啟動所有服務
deployment/manage.sh stop           # 停止所有服務
deployment/manage.sh restart        # 重啟所有服務

# 監控
deployment/manage.sh monitor        # 實時系統監控
deployment/manage.sh dashboard      # 打開Grafana儀表板
deployment/manage.sh logs -f        # 實時日誌

# 維護
deployment/manage.sh optimize       # 系統優化
deployment/manage.sh cleanup        # 清理舊數據
```

## 🖥️ 原生部署

### 特點
- ✅ 最佳性能
- ✅ 直接系統控制
- ✅ Systemd 管理
- ❌ 配置複雜

### 部署步驟

```bash
# 1. 確保root權限
sudo su

# 2. 執行部署
deployment/manage.sh deploy-native

# 3. 啟動服務
systemctl start outlook-summary
systemctl enable outlook-summary

# 4. 驗證
systemctl status outlook-summary
curl http://localhost:8000/health
```

### 系統服務管理

```bash
# 使用內建管理工具
outlook-summary start       # 啟動服務
outlook-summary stop        # 停止服務
outlook-summary status      # 查看狀態
outlook-summary logs        # 查看日誌
outlook-summary health      # 健康檢查
outlook-summary optimize    # 系統優化
```

### Systemd 服務配置

原生部署使用 systemd 服務管理，配置詳情：

```ini
# 服務啟動命令
ExecStart=/opt/outlook-summary/venv/bin/gunicorn \
    --bind 0.0.0.0:8000 \
    --workers 4 \
    --worker-class sync \
    --timeout 120 \
    --chdir /opt/outlook-summary \
    "frontend.app:create_app()"

# 環境變數
Environment=PYTHONPATH=/opt/outlook-summary/src:/opt/outlook-summary/frontend
Environment=OUTLOOK_ENV=production

# 檔案系統權限
ReadOnlyPaths=/opt/outlook-summary/src
ReadOnlyPaths=/opt/outlook-summary/frontend
ReadOnlyPaths=/opt/outlook-summary/config
```

## 🐋 Docker 部署

### 特點
- ✅ 輕量級
- ✅ 快速部署
- ✅ 環境隔離
- ❌ 無內建監控

### 部署步驟

```bash
# 1. 構建並部署
deployment/manage.sh deploy-docker

# 2. 驗證
docker ps | grep outlook-summary
docker logs outlook-summary

# 3. 健康檢查
deployment/manage.sh health
```

## 📊 監控和告警

### Prometheus 指標

```yaml
# 核心指標
outlook_process_pool_active_workers      # 活躍工作進程數
outlook_process_pool_completed_tasks     # 完成任務數
outlook_process_pool_failed_tasks        # 失敗任務數
outlook_system_cpu_usage_percent         # CPU使用率
outlook_system_memory_usage_percent      # 記憶體使用率
outlook_email_processing_duration        # 郵件處理時間
outlook_file_processing_duration         # 文件處理時間
```

### Grafana 儀表板

- **系統概覽**: CPU、記憶體、磁碟使用率
- **進程池監控**: 工作進程狀態、任務統計
- **業務指標**: 郵件處理量、成功率、響應時間
- **告警面板**: 異常狀態、性能瓶頸

### 告警規則

```yaml
# CPU使用率過高
cpu_usage_high:
  condition: cpu_usage > 90%
  duration: 5m
  action: 發送告警通知

# 記憶體使用率過高  
memory_usage_high:
  condition: memory_usage > 90%
  duration: 3m
  action: 自動清理 + 告警

# 任務失敗率過高
task_failure_rate_high:
  condition: failure_rate > 10%
  duration: 1m
  action: 重啟進程池 + 告警
```

## 🔧 性能調優

### 自動優化

```bash
# 執行系統優化
deployment/manage.sh optimize
```

### 手動調優

```bash
# 1. CPU調優
echo performance | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor

# 2. 記憶體調優
echo 10 | sudo tee /proc/sys/vm/swappiness
echo 1 | sudo tee /proc/sys/vm/overcommit_memory

# 3. 檔案系統調優
echo 131072 | sudo tee /proc/sys/fs/file-max
ulimit -n 65536
```

### 進程池調優

```python
# 根據硬體調整配置
OPTIMIZATION_CONFIGS = {
    "4核8線程": {
        "cpu_workers": 4,
        "io_workers": 16,
        "memory_per_worker": "128MB"
    },
    "8核16線程": {
        "cpu_workers": 8,
        "io_workers": 32,
        "memory_per_worker": "256MB"
    },
    "16核32線程": {
        "cpu_workers": 16,
        "io_workers": 64,
        "memory_per_worker": "512MB"
    }
}
```

## 🔍 故障排除

### 常見問題

#### 1. 服務無法啟動

```bash
# 檢查日誌
deployment/manage.sh logs

# 檢查端口占用
netstat -tulpn | grep 8000

# 檢查權限
ls -la /opt/outlook-summary/
```

#### 2. 性能問題

```bash
# 系統監控
deployment/manage.sh monitor

# 檢查資源使用
htop
iotop
```

#### 3. 記憶體洩漏

```bash
# 重啟服務
deployment/manage.sh restart

# 檢查記憶體使用
free -h
ps aux --sort=-%mem
```

### 日誌位置

```bash
# Docker Compose
docker-compose logs outlook-summary

# Docker
docker logs outlook-summary

# 原生部署
journalctl -u outlook-summary -f
tail -f /opt/outlook-summary/logs/app/*.log
```

## 🛡️ 安全配置

### 系統安全

```bash
# 防火牆設置
ufw allow 8000/tcp
ufw allow 22/tcp
ufw --force enable

# 用戶權限
useradd -r -s /bin/false outlook-user
chown -R outlook-user:outlook-user /opt/outlook-summary
```

### 應用安全

- ✅ 進程隔離 (ProcessPoolExecutor)
- ✅ 資源限制 (Memory、CPU、File descriptors)
- ✅ 安全沙箱 (Docker containers)
- ✅ 最小權限原則
- ✅ 輸入驗證和過濾

## 📝 運維檢查清單

### 日常檢查

- [ ] 服務健康狀態
- [ ] 系統資源使用率
- [ ] 錯誤日誌檢查
- [ ] 備份狀態確認

### 週期維護

- [ ] 日誌輪轉和清理
- [ ] 系統更新
- [ ] 性能指標分析
- [ ] 安全漏洞掃描

### 緊急響應

- [ ] 服務異常處理流程
- [ ] 數據恢復計劃
- [ ] 通知聯繫方式
- [ ] 備用方案準備

---

## 🎯 結論

這套多進程部署方案提供了：

- **高性能**: 充分利用多核心CPU資源
- **高可用**: 進程池自動故障恢復
- **易維護**: 完整的監控和管理工具
- **易擴展**: 支持水平和垂直擴展
- **生產就緒**: 完整的安全和運維支持

選擇適合您環境的部署方式，開始享受多進程帶來的性能提升！

---

**快速聯繫**: 如遇問題，請查看日誌或使用 `deployment/manage.sh health` 進行診斷。