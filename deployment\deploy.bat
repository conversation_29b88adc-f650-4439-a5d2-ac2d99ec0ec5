@echo off
REM Outlook Summary 多進程系統 Windows 部署腳本

echo.
echo 🚀 Outlook Summary 多進程系統部署工具 (Windows)
echo.

if "%1"=="" goto :show_help
if "%1"=="help" goto :show_help
if "%1"=="--help" goto :show_help
if "%1"=="-h" goto :show_help

if "%1"=="docker" goto :deploy_docker
if "%1"=="compose" goto :deploy_compose
if "%1"=="build" goto :build_image
if "%1"=="start" goto :start_service
if "%1"=="stop" goto :stop_service
if "%1"=="status" goto :show_status
if "%1"=="logs" goto :show_logs
if "%1"=="health" goto :health_check

echo ❌ 未知命令: %1
goto :show_help

:show_help
echo 📋 可用命令:
echo.
echo   docker     - Docker 部署
echo   compose    - Docker Compose 部署 (推薦)
echo   build      - 構建 Docker 鏡像
echo   start      - 啟動服務
echo   stop       - 停止服務
echo   status     - 查看狀態
echo   logs       - 查看日誌
echo   health     - 健康檢查
echo   help       - 顯示幫助
echo.
echo 📖 示例:
echo   deploy.bat compose     - 使用 Docker Compose 部署
echo   deploy.bat start       - 啟動服務
echo   deploy.bat health      - 健康檢查
echo.
goto :end

:deploy_docker
echo 🐳 開始 Docker 部署...
echo.

REM 檢查 Docker 是否安裝
where docker >nul 2>nul
if errorlevel 1 (
    echo ❌ 未找到 Docker，請先安裝 Docker Desktop
    goto :end
)

REM 構建鏡像
call :build_image

REM 停止現有容器
docker stop outlook-summary 2>nul
docker rm outlook-summary 2>nul

REM 啟動新容器
echo 📦 啟動 Docker 容器...
docker run -d ^
    --name outlook-summary ^
    --restart unless-stopped ^
    -p 8000:8000 ^
    -v "%cd%\..\logs:/app/logs" ^
    -v "%cd%\..\data:/app/data" ^
    outlook-summary:latest

if errorlevel 1 (
    echo ❌ 容器啟動失敗
    goto :end
)

echo ✅ Docker 部署完成！
echo.
echo 🔗 服務地址: http://localhost:8000
echo 🏥 健康檢查: http://localhost:8000/health
goto :end

:deploy_compose
echo 🎼 開始 Docker Compose 部署...
echo.

REM 檢查 Docker Compose 是否安裝
where docker-compose >nul 2>nul
if errorlevel 1 (
    echo ❌ 未找到 Docker Compose，請先安裝
    goto :end
)

REM 進入 Docker 目錄
pushd "%~dp0docker"

REM 停止現有服務
docker-compose down 2>nul

REM 構建並啟動服務
echo 🏗️ 構建並啟動所有服務...
docker-compose up -d --build

if errorlevel 1 (
    echo ❌ Docker Compose 部署失敗
    popd
    goto :end
)

echo ✅ Docker Compose 部署完成！
echo.
echo 🔗 服務地址:
echo   應用服務: http://localhost:8000
echo   健康檢查: http://localhost:8000/health
echo   監控面板: http://localhost:9090
echo   儀表板: http://localhost:3000 (admin/admin123)

popd
goto :end

:build_image
echo 🔨 構建 Docker 鏡像...

REM 檢查 Dockerfile 是否存在
if not exist "%~dp0docker\Dockerfile" (
    echo ❌ 未找到 Dockerfile
    goto :end
)

REM 檢查前端目錄是否存在
if not exist "%~dp0..\frontend" (
    echo ❌ 未找到前端目錄，請確保已完成前端重構
    goto :end
)

REM 構建鏡像
docker build -f "%~dp0docker\Dockerfile" -t outlook-summary:latest "%~dp0.."

if errorlevel 1 (
    echo ❌ 鏡像構建失敗
    goto :end
)

echo ✅ Docker 鏡像構建完成
goto :end

:start_service
echo 🚀 啟動服務...

REM 檢查是否有 Docker Compose
pushd "%~dp0docker"
if exist "docker-compose.yml" (
    docker-compose up -d
    if not errorlevel 1 echo ✅ 服務已啟動 (Docker Compose)
) else (
    REM 檢查是否有 Docker 容器
    docker start outlook-summary 2>nul
    if not errorlevel 1 (
        echo ✅ 服務已啟動 (Docker)
    ) else (
        echo ❌ 未找到可啟動的服務
    )
)
popd
goto :end

:stop_service
echo 🛑 停止服務...

REM 檢查是否有 Docker Compose
pushd "%~dp0docker"
if exist "docker-compose.yml" (
    docker-compose down
    if not errorlevel 1 echo ✅ 服務已停止 (Docker Compose)
) else (
    REM 停止 Docker 容器
    docker stop outlook-summary 2>nul
    if not errorlevel 1 (
        echo ✅ 服務已停止 (Docker)
    ) else (
        echo ❌ 未找到運行中的服務
    )
)
popd
goto :end

:show_status
echo 📊 查看服務狀態...
echo.

REM 檢查 Docker Compose 服務
pushd "%~dp0docker"
if exist "docker-compose.yml" (
    echo Docker Compose 服務狀態:
    docker-compose ps
    echo.
)
popd

REM 檢查 Docker 容器
echo Docker 容器狀態:
docker ps -a | findstr outlook-summary

REM 檢查端口
echo.
echo 端口檢查:
netstat -an | findstr :8000
goto :end

:show_logs
echo 📝 查看日誌...
echo.

REM 檢查是否有 Docker Compose
pushd "%~dp0docker"
if exist "docker-compose.yml" (
    docker-compose logs --tail=50 outlook-summary
) else (
    docker logs --tail=50 outlook-summary
)
popd
goto :end

:health_check
echo 🏥 執行健康檢查...
echo.

REM 使用 curl 檢查健康狀態
where curl >nul 2>nul
if errorlevel 1 (
    echo ❌ 未找到 curl，請安裝 curl 或使用瀏覽器訪問: http://localhost:8000/health
    goto :end
)

echo 檢查服務健康狀態...
curl -f -s http://localhost:8000/health

if errorlevel 1 (
    echo ❌ 健康檢查失敗，服務可能未啟動
    echo 請檢查: deploy.bat status
) else (
    echo.
    echo ✅ 健康檢查通過
)
goto :end

:end
echo.
pause