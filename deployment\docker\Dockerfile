# Multi-stage build for Outlook Summary Multi-Process System
FROM python:3.11-slim AS builder

# 設置構建參數
ARG BUILD_DATE
ARG VCS_REF
ARG VERSION

# 元數據標籤
LABEL maintainer="outlook-summary-team" \
      org.label-schema.build-date=$BUILD_DATE \
      org.label-schema.name="outlook-summary" \
      org.label-schema.description="Multi-process email processing system" \
      org.label-schema.version=$VERSION \
      org.label-schema.vcs-ref=$VCS_REF \
      org.label-schema.schema-version="1.0"

# 安裝系統依賴
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    g++ \
    libc6-dev \
    libffi-dev \
    libssl-dev \
    python3-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 設置Python環境
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONIOENCODING=utf-8 \
    PYTHONUTF8=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 創建虛擬環境
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 升級pip和安裝構建工具
RUN pip install --upgrade pip setuptools wheel

# 複製依賴文件
COPY requirements.txt /tmp/requirements.txt

# 安裝Python依賴
RUN pip install --no-cache-dir -r /tmp/requirements.txt

# Production stage
FROM python:3.11-slim AS production

# 安裝運行時依賴
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    dumb-init \
    && rm -rf /var/lib/apt/lists/*

# 創建應用用戶
RUN groupadd -r outlook --gid=1000 && \
    useradd -r -g outlook --uid=1000 --shell=/bin/bash outlook

# 設置工作目錄
WORKDIR /app

# 從builder階段複製虛擬環境
COPY --from=builder /opt/venv /opt/venv

# 設置環境變數
ENV PATH="/opt/venv/bin:$PATH" \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONIOENCODING=utf-8 \
    PYTHONUTF8=1 \
    OUTLOOK_ENV=production

# 創建必要目錄
RUN mkdir -p /app/{src,config,logs,temp,data} && \
    mkdir -p /app/logs/{app,worker,system} && \
    mkdir -p /app/temp/{uploads,processing,downloads} && \
    chown -R outlook:outlook /app

# 複製應用代碼
COPY --chown=outlook:outlook src/ /app/src/
COPY --chown=outlook:outlook frontend/ /app/frontend/
COPY --chown=outlook:outlook config/ /app/config/
COPY --chown=outlook:outlook deployment/docker/entrypoint.sh /app/entrypoint.sh

# 設置權限
RUN chmod +x /app/entrypoint.sh

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 切換到應用用戶
USER outlook

# 暴露端口
EXPOSE 8000

# 設置入口點
ENTRYPOINT ["/usr/bin/dumb-init", "--"]
CMD ["/app/entrypoint.sh"]