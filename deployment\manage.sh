#!/bin/bash
# manage.sh - Outlook Summary 多進程系統管理腳本

set -e

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
APP_NAME="outlook-summary"
DOCKER_COMPOSE_FILE="$SCRIPT_DIR/docker/docker-compose.yml"
SERVICE_NAME="outlook-summary.service"

# 顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日誌函數
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_header() { echo -e "${PURPLE}[HEADER]${NC} $1"; }

# 顯示使用說明
show_usage() {
    cat << EOF
🚀 Outlook Summary 多進程系統管理工具

用法: $0 <command> [options]

📋 可用命令:

部署相關:
  deploy-native     使用systemd原生部署
  deploy-docker     使用Docker部署
  deploy-compose    使用Docker Compose部署

服務管理:
  start            啟動服務
  stop             停止服務
  restart          重啟服務
  status           查看服務狀態
  logs             查看日誌
  health           健康檢查

監控相關:
  monitor          實時監控系統資源
  metrics          查看性能指標
  dashboard        打開Grafana儀表板

維護相關:
  optimize         系統優化
  cleanup          清理日誌和臨時文件
  backup           備份配置和數據
  update           更新應用

開發相關:
  dev-setup        開發環境設置
  test             運行測試
  test-deploy      測試部署配置
  build            構建Docker鏡像

其他:
  help             顯示此幫助
  version          顯示版本信息

📖 示例:
  $0 deploy-docker          # Docker部署
  $0 start                  # 啟動服務
  $0 monitor                # 實時監控
  $0 logs --follow          # 實時查看日誌

EOF
}

# 檢查依賴
check_dependencies() {
    local missing_deps=()
    
    # 檢查基本工具
    command -v curl >/dev/null 2>&1 || missing_deps+=("curl")
    command -v git >/dev/null 2>&1 || missing_deps+=("git")
    
    # 根據部署方式檢查特定依賴
    case "$1" in
        docker)
            command -v docker >/dev/null 2>&1 || missing_deps+=("docker")
            command -v docker-compose >/dev/null 2>&1 || missing_deps+=("docker-compose")
            ;;
        native)
            command -v systemctl >/dev/null 2>&1 || missing_deps+=("systemctl")
            command -v python3.11 >/dev/null 2>&1 || missing_deps+=("python3.11")
            ;;
    esac
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "缺少依賴項: ${missing_deps[*]}"
        log_info "請安裝缺少的依賴項後重試"
        exit 1
    fi
}

# 原生部署
deploy_native() {
    log_header "🏗️ 開始原生部署..."
    
    check_dependencies native
    
    if [[ $EUID -ne 0 ]]; then
        log_error "原生部署需要root權限"
        exit 1
    fi
    
    # 執行部署腳本
    if [[ -f "$SCRIPT_DIR/deploy.sh" ]]; then
        chmod +x "$SCRIPT_DIR/deploy.sh"
        bash "$SCRIPT_DIR/deploy.sh"
    else
        log_error "找不到部署腳本: $SCRIPT_DIR/deploy.sh"
        exit 1
    fi
    
    log_success "原生部署完成！"
}

# Docker部署
deploy_docker() {
    log_header "🐳 開始Docker部署..."
    
    check_dependencies docker
    
    # 構建鏡像
    build_docker_image
    
    # 運行容器
    log_info "啟動Docker容器..."
    docker run -d \
        --name $APP_NAME \
        --restart unless-stopped \
        -p 8000:8000 \
        -v "$PROJECT_ROOT/logs:/app/logs" \
        -v "$PROJECT_ROOT/data:/app/data" \
        $APP_NAME:latest
    
    # 等待服務啟動
    sleep 10
    
    # 健康檢查
    if docker exec $APP_NAME /app/entrypoint.sh health-check; then
        log_success "Docker部署完成！"
        docker ps | grep $APP_NAME
    else
        log_error "Docker部署失敗"
        docker logs $APP_NAME
        exit 1
    fi
}

# Docker Compose部署
deploy_compose() {
    log_header "🎼 開始Docker Compose部署..."
    
    check_dependencies docker
    
    cd "$SCRIPT_DIR/docker"
    
    # 設置環境變數
    export BUILD_DATE=$(date -u +%Y-%m-%dT%H:%M:%SZ)
    export VCS_REF=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    export VERSION=${VERSION:-latest}
    
    # 構建並啟動服務
    log_info "構建和啟動所有服務..."
    docker-compose -f docker-compose.yml up -d --build
    
    # 等待服務啟動
    log_info "等待服務就緒..."
    sleep 30
    
    # 檢查服務狀態
    if docker-compose -f docker-compose.yml ps | grep -q "Up"; then
        log_success "Docker Compose部署完成！"
        docker-compose -f docker-compose.yml ps
        
        log_info "服務地址:"
        log_info "  應用: http://localhost:8000"
        log_info "  監控: http://localhost:9090"
        log_info "  儀表板: http://localhost:3000 (admin/admin123)"
    else
        log_error "Docker Compose部署失敗"
        docker-compose -f docker-compose.yml logs
        exit 1
    fi
}

# 構建Docker鏡像
build_docker_image() {
    log_info "構建Docker鏡像..."
    
    cd "$PROJECT_ROOT"
    
    # 設置構建參數
    BUILD_ARGS="--build-arg BUILD_DATE=$(date -u +%Y-%m-%dT%H:%M:%SZ)"
    BUILD_ARGS="$BUILD_ARGS --build-arg VCS_REF=$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')"
    BUILD_ARGS="$BUILD_ARGS --build-arg VERSION=${VERSION:-latest}"
    
    # 構建鏡像
    docker build \
        $BUILD_ARGS \
        -f deployment/docker/Dockerfile \
        -t $APP_NAME:latest \
        -t $APP_NAME:${VERSION:-latest} \
        .
    
    log_success "Docker鏡像構建完成"
}

# 服務管理函數
start_service() {
    log_info "啟動服務..."
    
    if command -v docker-compose >/dev/null 2>&1 && [[ -f "$DOCKER_COMPOSE_FILE" ]]; then
        cd "$SCRIPT_DIR/docker"
        docker-compose up -d
    elif command -v docker >/dev/null 2>&1 && docker ps -a | grep -q $APP_NAME; then
        docker start $APP_NAME
    elif command -v systemctl >/dev/null 2>&1; then
        sudo systemctl start $SERVICE_NAME
    else
        log_error "無法識別部署方式"
        exit 1
    fi
    
    log_success "服務已啟動"
}

stop_service() {
    log_info "停止服務..."
    
    if command -v docker-compose >/dev/null 2>&1 && [[ -f "$DOCKER_COMPOSE_FILE" ]]; then
        cd "$SCRIPT_DIR/docker"
        docker-compose down
    elif command -v docker >/dev/null 2>&1 && docker ps | grep -q $APP_NAME; then
        docker stop $APP_NAME
    elif command -v systemctl >/dev/null 2>&1; then
        sudo systemctl stop $SERVICE_NAME
    else
        log_error "無法識別部署方式"
        exit 1
    fi
    
    log_success "服務已停止"
}

restart_service() {
    log_info "重啟服務..."
    stop_service
    sleep 2
    start_service
    log_success "服務已重啟"
}

# 查看服務狀態
show_status() {
    log_info "查看服務狀態..."
    
    if command -v docker-compose >/dev/null 2>&1 && [[ -f "$DOCKER_COMPOSE_FILE" ]]; then
        cd "$SCRIPT_DIR/docker"
        docker-compose ps
        echo ""
        docker-compose top
    elif command -v docker >/dev/null 2>&1 && docker ps -a | grep -q $APP_NAME; then
        docker ps | grep $APP_NAME
        echo ""
        docker stats --no-stream $APP_NAME
    elif command -v systemctl >/dev/null 2>&1; then
        sudo systemctl status $SERVICE_NAME --no-pager
    else
        log_error "無法識別部署方式"
        exit 1
    fi
}

# 查看日誌
show_logs() {
    local follow_flag=""
    if [[ "$1" == "--follow" || "$1" == "-f" ]]; then
        follow_flag="-f"
    fi
    
    log_info "查看日誌..."
    
    if command -v docker-compose >/dev/null 2>&1 && [[ -f "$DOCKER_COMPOSE_FILE" ]]; then
        cd "$SCRIPT_DIR/docker"
        docker-compose logs $follow_flag outlook-summary
    elif command -v docker >/dev/null 2>&1 && docker ps | grep -q $APP_NAME; then
        docker logs $follow_flag $APP_NAME
    elif command -v systemctl >/dev/null 2>&1; then
        sudo journalctl -u $SERVICE_NAME $follow_flag
    else
        log_error "無法識別部署方式"
        exit 1
    fi
}

# 健康檢查
health_check() {
    log_info "執行健康檢查..."
    
    local health_url="http://localhost:8000/health"
    local max_attempts=3
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        log_info "健康檢查嘗試 $attempt/$max_attempts..."
        
        if curl -f -s "$health_url" | python3 -m json.tool 2>/dev/null; then
            log_success "健康檢查通過"
            return 0
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            log_error "健康檢查失敗"
            return 1
        fi
        
        attempt=$((attempt + 1))
        sleep 5
    done
}

# 實時監控
monitor_system() {
    log_header "📊 系統監控 (按Ctrl+C退出)"
    
    while true; do
        clear
        echo "=== Outlook Summary 系統監控 ==="
        echo "時間: $(date)"
        echo ""
        
        # 系統資源
        echo "🖥️  系統資源:"
        echo "CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
        echo "記憶體: $(free | grep Mem | awk '{printf("%.1f%%", $3/$2 * 100.0)}')"
        echo "磁碟: $(df / | tail -1 | awk '{print $5}')"
        echo ""
        
        # 服務狀態
        echo "🔧 服務狀態:"
        if curl -f -s "http://localhost:8000/health" > /dev/null 2>&1; then
            echo "應用狀態: ✅ 健康"
        else
            echo "應用狀態: ❌ 異常"
        fi
        echo ""
        
        # 進程信息
        echo "🔄 進程信息:"
        ps aux | grep -E "(python|outlook)" | grep -v grep | head -5
        echo ""
        
        sleep 5
    done
}

# 系統優化
optimize_system() {
    log_header "⚡ 系統優化"
    
    if [[ $EUID -eq 0 ]]; then
        # 設置系統參數
        log_info "調整系統參數..."
        
        # 檔案描述符限制
        echo "* soft nofile 65536" >> /etc/security/limits.conf
        echo "* hard nofile 65536" >> /etc/security/limits.conf
        
        # 系統參數
        sysctl -w fs.file-max=131072
        sysctl -w vm.swappiness=10
        sysctl -w vm.overcommit_memory=1
        
        log_success "系統優化完成"
    else
        log_warning "需要root權限進行系統優化"
        
        # 用戶級優化
        log_info "執行用戶級優化..."
        ulimit -n 65536 2>/dev/null || log_warning "無法設置檔案描述符限制"
        
        log_success "用戶級優化完成"
    fi
}

# 清理功能
cleanup_system() {
    log_header "🧹 系統清理"
    
    # 清理日誌
    log_info "清理舊日誌..."
    find "$PROJECT_ROOT/logs" -name "*.log" -mtime +7 -delete 2>/dev/null || true
    
    # 清理臨時文件
    log_info "清理臨時文件..."
    find "$PROJECT_ROOT/temp" -type f -mtime +1 -delete 2>/dev/null || true
    
    # Docker清理
    if command -v docker >/dev/null 2>&1; then
        log_info "清理Docker資源..."
        docker system prune -f >/dev/null 2>&1 || true
    fi
    
    log_success "系統清理完成"
}

# 打開儀表板
open_dashboard() {
    local grafana_url="http://localhost:3000"
    
    log_info "打開Grafana儀表板..."
    
    if command -v xdg-open >/dev/null 2>&1; then
        xdg-open "$grafana_url"
    elif command -v open >/dev/null 2>&1; then
        open "$grafana_url"
    else
        log_info "請在瀏覽器中打開: $grafana_url"
        log_info "用戶名: admin, 密碼: admin123"
    fi
}

# 測試部署配置
test_deployment() {
    log_header "🧪 測試部署配置"
    
    if [[ -f "$SCRIPT_DIR/test-deployment.sh" ]]; then
        chmod +x "$SCRIPT_DIR/test-deployment.sh"
        bash "$SCRIPT_DIR/test-deployment.sh"
    else
        log_error "找不到部署測試腳本: $SCRIPT_DIR/test-deployment.sh"
        exit 1
    fi
}

# 顯示版本信息
show_version() {
    log_info "Outlook Summary 多進程系統"
    log_info "版本: ${VERSION:-dev}"
    log_info "構建: $(date)"
    log_info "Git: $(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')"
}

# 主函數
main() {
    case "${1:-help}" in
        deploy-native)
            deploy_native
            ;;
        deploy-docker)
            deploy_docker
            ;;
        deploy-compose)
            deploy_compose
            ;;
        build)
            build_docker_image
            ;;
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs "$2"
            ;;
        health)
            health_check
            ;;
        monitor)
            monitor_system
            ;;
        dashboard)
            open_dashboard
            ;;
        optimize)
            optimize_system
            ;;
        cleanup)
            cleanup_system
            ;;
        test-deploy)
            test_deployment
            ;;
        version)
            show_version
            ;;
        help|--help|-h)
            show_usage
            ;;
        *)
            log_error "未知命令: $1"
            show_usage
            exit 1
            ;;
    esac
}

# 執行主函數
main "$@"