[Unit]
Description=Outlook Summary Multi-Process Email Processing Service
Documentation=https://github.com/outlook-summary/docs
After=network.target network-online.target
Wants=network-online.target
Requires=network.target

[Service]
Type=simple
User=outlook-user
Group=outlook-user
WorkingDirectory=/opt/outlook-summary
Environment=PYTHONPATH=/opt/outlook-summary/src:/opt/outlook-summary/frontend
Environment=OUTLOOK_ENV=production
Environment=PYTHONUNBUFFERED=1
Environment=PYTHONIOENCODING=utf-8
Environment=PYTHONUTF8=1

# 執行命令
ExecStartPre=/opt/outlook-summary/scripts/optimize.sh
ExecStart=/opt/outlook-summary/venv/bin/gunicorn --bind 0.0.0.0:8000 --workers 4 --worker-class sync --timeout 120 --chdir /opt/outlook-summary "frontend.app:create_app()"
ExecReload=/bin/kill -HUP $MAINPID
ExecStop=/bin/kill -TERM $MAINPID

# 進程管理
KillMode=mixed
KillSignal=SIGTERM
TimeoutStartSec=60
TimeoutStopSec=30
RestartSec=5
Restart=always
RestartPreventExitStatus=0

# 資源限制
LimitNOFILE=65536
LimitNPROC=32768
LimitCORE=0

# 記憶體限制 (4GB)
MemoryMax=4G
MemoryHigh=3G

# CPU限制 (800% = 8核心)
CPUQuota=800%
CPUAccounting=true

# IO限制
IOAccounting=true
IOWeight=500

# 安全設置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectKernelLogs=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictSUIDSGID=true

# 檔案系統權限
ReadWritePaths=/opt/outlook-summary/logs
ReadWritePaths=/opt/outlook-summary/temp
ReadWritePaths=/opt/outlook-summary/data
ReadOnlyPaths=/opt/outlook-summary/src
ReadOnlyPaths=/opt/outlook-summary/frontend
ReadOnlyPaths=/opt/outlook-summary/config

# 網路安全
RestrictAddressFamilies=AF_INET AF_INET6 AF_UNIX
IPAddressDeny=any
IPAddressAllow=localhost
IPAddressAllow=127.0.0.1
IPAddressAllow=::1

# 系統調用限制
SystemCallFilter=@system-service
SystemCallErrorNumber=EPERM

# 私有目錄
PrivateTmp=true
PrivateDevices=true
PrivateUsers=false

# 設備策略
DevicePolicy=closed

[Install]
WantedBy=multi-user.target
Alias=outlook.service