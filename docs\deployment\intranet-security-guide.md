# 內網環境安全配置指南

## 📋 概述

本應用程式設計用於內網環境，允許內網用戶訪問，外部安全由防火牆處理。

## 🌐 網路配置

### 內網綁定策略
```python
# Flask 配置 - 允許內網訪問
HOST = '0.0.0.0'  # 綁定所有網路介面
PORT = 8000       # 標準服務端口
```

```ini
# Systemd 服務 - 一致的網路綁定
ExecStart=.../gunicorn --bind 0.0.0.0:8000 ...
```

### 安全分層
```
┌─────────────────┐
│   外部防火牆      │ ← 外部安全控制
├─────────────────┤
│   內網環境       │ ← 信任的網路環境
│  ┌─────────────┐ │
│  │ 應用程式     │ │ ← 0.0.0.0:8000
│  │ (Flask)    │ │
│  └─────────────┘ │
└─────────────────┘
```

## 🔒 安全配置

### 1. SECRET_KEY 管理
```bash
# 生產環境必須設定
export SECRET_KEY="your-secure-secret-key-for-production"
export FLASK_ENV="production"
```

### 2. Cookie 安全性（內網調整）
```python
# 內網環境 Cookie 配置
SESSION_COOKIE_SECURE = False      # 內網可能使用 HTTP
SESSION_COOKIE_HTTPONLY = True     # 防止 XSS
SESSION_COOKIE_SAMESITE = 'Lax'    # CSRF 防護
```

### 3. 資源限制
```ini
# 合理的資源限制
MemoryMax=2G        # 2GB 記憶體限制
MemoryHigh=1.5G     # 1.5GB 軟限制
CPUQuota=400%       # 4 核心 CPU 限制
```

## 🛡️ 安全最佳實踐

### 內網環境安全原則
1. **網路分層**: 外部防火牆負責外部威脅防護
2. **應用安全**: 應用程式負責內部安全控制
3. **訪問控制**: 通過應用程式層面的認證授權
4. **監控記錄**: 完整的訪問和操作日誌

### 推薦的外部防火牆規則
```bash
# 僅允許內網訪問
iptables -A INPUT -s ***********/16 -p tcp --dport 8000 -j ACCEPT
iptables -A INPUT -s 10.0.0.0/8 -p tcp --dport 8000 -j ACCEPT
iptables -A INPUT -s **********/12 -p tcp --dport 8000 -j ACCEPT
iptables -A INPUT -p tcp --dport 8000 -j DROP
```

## 🧪 安全驗證

### 自動化檢查
```bash
# 執行內網環境安全檢查
python scripts/security_config_check.py
```

### 預期結果
```
🔍 檢查 Flask 配置安全性（內網環境）...
✅ 開發環境 SECRET_KEY 配置正確
✅ 內網環境網路綁定配置正確 (0.0.0.0)
✅ Cookie HttpOnly 設定正確
ℹ️ 內網環境不強制 HTTPS，由外部防火牆處理安全性

🔍 檢查部署配置（內網環境）...
✅ Systemd 服務網路綁定配置正確 (0.0.0.0:8000)
✅ Systemd 記憶體限制設定合理
✅ Systemd CPU 限制設定合理
✅ Systemd 安全設定正確
```

## 📊 內網安全評估

| 安全層面 | 配置狀態 | 說明 |
|---------|---------|------|
| 網路綁定 | ✅ 正確 | 0.0.0.0 允許內網訪問 |
| SECRET_KEY | ✅ 安全 | 生產環境強制設定 |
| Cookie 安全 | ✅ 適當 | 內網環境優化配置 |
| 資源限制 | ✅ 合理 | 防止資源耗盡 |
| 程序隔離 | ✅ 完整 | Systemd 安全設定 |
| 外部防護 | 🔧 外部 | 由防火牆負責 |

**整體安全評分**: 8.5/10 (內網環境優秀)

## 🚀 部署檢查清單

### 部署前確認
- [ ] SECRET_KEY 環境變數已設定
- [ ] 防火牆規則已配置
- [ ] 內網 DNS 解析正確
- [ ] 資源限制適合硬體環境
- [ ] 監控和日誌系統就緒

### 部署後驗證
- [ ] 內網用戶可以正常訪問
- [ ] 外部訪問被防火牆阻擋
- [ ] 所有功能模組正常運作
- [ ] 系統資源使用正常
- [ ] 日誌記錄正常

## 🔧 故障排除

### 常見問題
1. **內網無法訪問**: 檢查防火牆和網路配置
2. **外部意外訪問**: 檢查防火牆規則
3. **性能問題**: 調整資源限制
4. **會話問題**: 檢查 SECRET_KEY 設定

### 監控指標
- 內網連接數量
- 資源使用率
- 錯誤日誌頻率
- 響應時間

---

**注意**: 此配置專為內網環境設計，外部安全完全依賴防火牆。請確保防火牆配置正確且定期更新。
