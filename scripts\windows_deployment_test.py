#!/usr/bin/env python3
"""
Windows 環境部署測試腳本
測試 Flask 應用程式在 Windows 環境下的部署和運行
"""

import os
import sys
import time
import requests
import subprocess
import threading
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class WindowsDeploymentTester:
    def __init__(self):
        self.test_port = 5555
        self.test_host = '127.0.0.1'
        self.flask_process = None
        self.test_results = []
        
    def log_result(self, test_name, success, message=""):
        """記錄測試結果"""
        status = "✅ 通過" if success else "❌ 失敗"
        result = f"{status} {test_name}"
        if message:
            result += f": {message}"
        print(result)
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message
        })
    
    def test_flask_import(self):
        """測試 Flask 應用程式導入"""
        print("🧪 測試 Flask 應用程式導入...")
        try:
            from frontend.app import create_app
            app = create_app('development')
            self.log_result("Flask 應用程式導入", True, f"應用程式名稱: {app.name}")
            return True
        except Exception as e:
            self.log_result("Flask 應用程式導入", False, str(e))
            return False
    
    def test_config_loading(self):
        """測試配置載入"""
        print("\n🧪 測試配置載入...")
        try:
            from frontend.config import DevelopmentConfig, ProductionConfig
            
            # 測試開發環境配置
            dev_config = DevelopmentConfig()
            self.log_result("開發環境配置載入", True, f"DEBUG={dev_config.DEBUG}")
            
            # 測試生產環境配置
            prod_config = ProductionConfig()
            self.log_result("生產環境配置載入", True, f"DEBUG={prod_config.DEBUG}")
            
            return True
        except Exception as e:
            self.log_result("配置載入", False, str(e))
            return False
    
    def start_flask_server(self):
        """啟動 Flask 測試伺服器"""
        print(f"\n🚀 啟動 Flask 測試伺服器 (端口 {self.test_port})...")
        
        def run_server():
            try:
                from frontend.app import create_app
                app = create_app('development')
                app.run(host=self.test_host, port=self.test_port, debug=False, use_reloader=False)
            except Exception as e:
                print(f"❌ Flask 伺服器啟動失敗: {e}")
        
        # 在背景執行緒中啟動伺服器
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        # 等待伺服器啟動
        time.sleep(3)
        
        # 檢查伺服器是否啟動
        try:
            response = requests.get(f"http://{self.test_host}:{self.test_port}/health", timeout=5)
            if response.status_code == 200:
                self.log_result("Flask 伺服器啟動", True, f"健康檢查返回 {response.status_code}")
                return True
            else:
                self.log_result("Flask 伺服器啟動", False, f"健康檢查返回 {response.status_code}")
                return False
        except Exception as e:
            self.log_result("Flask 伺服器啟動", False, str(e))
            return False
    
    def test_health_endpoint(self):
        """測試健康檢查端點"""
        print("\n🧪 測試健康檢查端點...")
        try:
            response = requests.get(f"http://{self.test_host}:{self.test_port}/health", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                status = data.get('status', 'unknown')
                modules = data.get('modules', {})
                module_count = len(modules)
                
                self.log_result("健康檢查端點", True, 
                              f"狀態: {status}, 模組數量: {module_count}")
                return True
            else:
                self.log_result("健康檢查端點", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_result("健康檢查端點", False, str(e))
            return False
    
    def test_module_endpoints(self):
        """測試各模組端點"""
        print("\n🧪 測試各模組端點...")
        
        modules = [
            ('email', '/email/'),
            ('analytics', '/analytics/'),
            ('files', '/files/'),
            ('eqc', '/eqc/'),
            ('tasks', '/tasks/'),
            ('monitoring', '/monitoring/')
        ]
        
        success_count = 0
        for module_name, endpoint in modules:
            try:
                response = requests.get(f"http://{self.test_host}:{self.test_port}{endpoint}", 
                                      timeout=5)
                if response.status_code in [200, 302]:  # 200 或重定向都算成功
                    self.log_result(f"{module_name} 模組端點", True, f"HTTP {response.status_code}")
                    success_count += 1
                else:
                    self.log_result(f"{module_name} 模組端點", False, f"HTTP {response.status_code}")
            except Exception as e:
                self.log_result(f"{module_name} 模組端點", False, str(e))
        
        return success_count == len(modules)
    
    def test_static_resources(self):
        """測試靜態資源載入"""
        print("\n🧪 測試靜態資源載入...")
        
        static_files = [
            '/static/shared/css/style.css',
            '/static/shared/js/common.js',
        ]
        
        success_count = 0
        for static_file in static_files:
            try:
                response = requests.get(f"http://{self.test_host}:{self.test_port}{static_file}", 
                                      timeout=5)
                if response.status_code == 200:
                    self.log_result(f"靜態資源 {static_file}", True, "載入成功")
                    success_count += 1
                else:
                    self.log_result(f"靜態資源 {static_file}", False, f"HTTP {response.status_code}")
            except Exception as e:
                self.log_result(f"靜態資源 {static_file}", False, str(e))
        
        # 即使部分靜態資源失敗也不算致命錯誤
        return True
    
    def test_gunicorn_compatibility(self):
        """測試 Gunicorn 兼容性（Windows 環境跳過）"""
        print("\n🧪 測試 Gunicorn 兼容性...")
        
        # Windows 環境下 Gunicorn 支援有限，跳過實際測試
        self.log_result("Gunicorn 兼容性", True, "Windows 環境跳過實際測試")
        return True
    
    def run_all_tests(self):
        """執行所有測試"""
        print("🧪 開始 Windows 環境部署測試...\n")
        
        tests = [
            self.test_flask_import,
            self.test_config_loading,
            self.start_flask_server,
            self.test_health_endpoint,
            self.test_module_endpoints,
            self.test_static_resources,
            self.test_gunicorn_compatibility,
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
        
        # 輸出測試結果摘要
        print("\n" + "="*60)
        print("📋 Windows 部署測試結果摘要")
        print("="*60)
        
        print(f"總測試數量: {total}")
        print(f"通過測試: {passed}")
        print(f"失敗測試: {total - passed}")
        print(f"成功率: {passed/total*100:.1f}%")
        
        if passed == total:
            print("\n🎉 所有測試都通過！Windows 環境部署準備就緒。")
            return True
        else:
            print(f"\n⚠️ 有 {total - passed} 個測試失敗，請檢查上述錯誤訊息。")
            return False

def main():
    """主要測試函數"""
    tester = WindowsDeploymentTester()
    success = tester.run_all_tests()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
